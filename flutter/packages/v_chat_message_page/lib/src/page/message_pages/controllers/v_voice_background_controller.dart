// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_chat_voice_player/v_chat_voice_player.dart';

/// Enhanced voice controller that supports background playback
class VVoiceBackgroundPlayerController {
  final _voiceControllers = <VVoiceMessageController>[];
  final String? Function(String localId) onVoiceNeedToPlayNext;
  final VVoiceBackgroundService _backgroundService = VVoiceBackgroundService.instance;
  
  String? _currentPlayingId;
  StreamSubscription? _positionSubscription;
  StreamSubscription? _playerStateSubscription;

  VVoiceBackgroundPlayerController(this.onVoiceNeedToPlayNext);

  VVoiceMessageController? getById(String id) =>
      _voiceControllers.firstWhereOrNull((e) => e.id == id);

  VVoiceMessageController getVoiceController(VVoiceMessage voiceMessage) {
    final oldController = getById(voiceMessage.localId);

    if (oldController != null) return oldController;

    final controller = VVoiceBackgroundMessageController(
      id: voiceMessage.localId,
      audioSrc: voiceMessage.data.fileSource,
      onComplete: (String localId) {
        _onVoiceComplete(localId);
      },
      maxDuration: voiceMessage.data.durationObj,
      onPlaying: _onPlaying,
      backgroundService: _backgroundService,
      senderName: _getSenderName(voiceMessage),
    );
    _voiceControllers.add(controller);
    return controller;
  }

  String _getSenderName(VVoiceMessage voiceMessage) {
    // Try to get sender name from message
    // This might need to be adjusted based on your message structure
    return voiceMessage.senderName ?? 'Voice Message';
  }

  void _onPlaying(String id) {
    _currentPlayingId = id;
    
    // Pause all other controllers
    for (final controller in _voiceControllers) {
      if (controller.id != id) {
        controller.pausePlaying();
      }
    }
  }

  void _onVoiceComplete(String localId) {
    _currentPlayingId = null;
    
    // Try to play next voice note
    final nextId = onVoiceNeedToPlayNext(localId);
    if (nextId != null) {
      getById(nextId)?.initAndPlay();
    }
  }

  void pauseAll() {
    for (final c in _voiceControllers) {
      c.pausePlaying();
    }
    _backgroundService.pauseCurrentVoiceNote();
    _currentPlayingId = null;
  }

  void close() {
    _positionSubscription?.cancel();
    _playerStateSubscription?.cancel();
    
    for (final c in _voiceControllers) {
      c.dispose();
    }
    
    _backgroundService.stopCurrentVoiceNote();
    _currentPlayingId = null;
  }

  /// Check if any voice note is currently playing
  bool get isAnyPlaying => _currentPlayingId != null;

  /// Get currently playing voice note ID
  String? get currentPlayingId => _currentPlayingId;
}

/// Enhanced voice message controller that uses background service
class VVoiceBackgroundMessageController extends VVoiceMessageController {
  final VVoiceBackgroundService backgroundService;
  final String senderName;
  
  VVoiceBackgroundMessageController({
    required super.id,
    required super.audioSrc,
    required super.maxDuration,
    super.onComplete,
    super.onPlaying,
    required this.backgroundService,
    required this.senderName,
  });

  @override
  Future<void> initAndPlay() async {
    try {
      // Use background service for playback
      await backgroundService.playVoiceNote(
        voiceId: id,
        source: audioSrc,
        maxDuration: maxDuration,
        senderName: senderName,
        onComplete: (voiceId) {
          onComplete?.call(voiceId);
        },
        onPlaying: (voiceId) {
          onPlaying?.call(voiceId);
        },
      );
      
      // Update UI state
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error playing voice note: $e');
      }
    }
  }

  @override
  Future<void> pausePlaying() async {
    if (backgroundService.isVoiceNotePlaying(id)) {
      await backgroundService.pauseCurrentVoiceNote();
    }
    notifyListeners();
  }

  @override
  Future<void> resumePlaying() async {
    if (backgroundService.isVoiceNotePlaying(id)) {
      await backgroundService.resumeCurrentVoiceNote();
    } else {
      // If this voice note is not currently playing, start it
      await initAndPlay();
    }
    notifyListeners();
  }

  @override
  Future<void> stopPlaying() async {
    if (backgroundService.isVoiceNotePlaying(id)) {
      await backgroundService.stopCurrentVoiceNote();
    }
    notifyListeners();
  }

  @override
  Future<void> seekTo(Duration position) async {
    if (backgroundService.isVoiceNotePlaying(id)) {
      await backgroundService.seek(position);
    }
    notifyListeners();
  }

  @override
  bool get isPlaying => backgroundService.isVoiceNotePlaying(id);

  @override
  Duration get currentPosition {
    if (backgroundService.isVoiceNotePlaying(id)) {
      return backgroundService.position;
    }
    return Duration.zero;
  }

  @override
  Duration? get duration {
    if (backgroundService.isVoiceNotePlaying(id)) {
      return backgroundService.duration ?? maxDuration;
    }
    return maxDuration;
  }

  @override
  Stream<Duration> get positionStream {
    if (backgroundService.isVoiceNotePlaying(id)) {
      return backgroundService.positionStream;
    }
    return Stream.value(Duration.zero);
  }

  @override
  bool get isInitialized => true; // Background service handles initialization

  @override
  bool get isCompleted {
    if (backgroundService.isVoiceNotePlaying(id)) {
      return !backgroundService.isPlaying;
    }
    return false;
  }

  @override
  double get playbackSpeed => 1.0; // Default speed

  @override
  Future<void> setPlaybackSpeed(double speed) async {
    // Background service doesn't support speed change in this implementation
    // Could be extended if needed
  }

  @override
  void dispose() {
    // Don't dispose the background service as it's shared
    super.dispose();
  }
}
