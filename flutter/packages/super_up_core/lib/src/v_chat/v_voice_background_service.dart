// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'dart:io';

import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:just_audio/just_audio.dart';
import 'package:v_platform/v_platform.dart';

/// Background audio service for voice notes that allows playback to continue
/// when the app is in the background or when users navigate away from chat
class VVoiceBackgroundService extends BaseAudioHandler
    with <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>k<PERSON><PERSON><PERSON> {
  static VVoiceBackgroundService? _instance;
  static VVoiceBackgroundService get instance =>
      _instance ??= VVoiceBackgroundService._();

  VVoiceBackgroundService._();

  final _audioPlayer = AudioPlayer();
  final _playbackStateController = StreamController<PlaybackState>.broadcast();
  final _mediaItemController = StreamController<MediaItem?>.broadcast();

  String? _currentVoiceId;
  VPlatformFile? _currentSource;
  Duration? _maxDuration;
  Function(String voiceId)? _onComplete;
  Function(String voiceId)? _onPlaying;

  /// Initialize the background audio service
  static Future<void> initialize() async {
    if (!VPlatforms.isMobile) return;

    try {
      _instance = await AudioService.init(
        builder: () => VVoiceBackgroundService._(),
        config: const AudioServiceConfig(
          androidNotificationChannelId: 'com.super_up.voice_notes',
          androidNotificationChannelName: 'Voice Notes',
          androidNotificationChannelDescription: 'Voice note playback controls',
          androidNotificationOngoing: false,
          androidStopForegroundOnPause: true,
          androidNotificationIcon: 'drawable/ic_notification',
          fastForwardInterval: Duration(seconds: 10),
          rewindInterval: Duration(seconds: 10),
        ),
      );
      await _instance!._init();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize VVoiceBackgroundService: $e');
      }
    }
  }

  Future<void> _init() async {
    // Configure audio session for voice playback
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration(
      avAudioSessionCategory: AVAudioSessionCategory.playback,
      avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.duckOthers,
      avAudioSessionMode: AVAudioSessionMode.spokenAudio,
      avAudioSessionRouteSharingPolicy:
          AVAudioSessionRouteSharingPolicy.defaultPolicy,
      avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
      androidAudioAttributes: AndroidAudioAttributes(
        contentType: AndroidAudioContentType.speech,
        flags: AndroidAudioFlags.none,
        usage: AndroidAudioUsage.media,
      ),
      androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      androidWillPauseWhenDucked: true,
    ));

    // Listen to audio player state changes
    _audioPlayer.playbackEventStream.listen(_broadcastState);
    _audioPlayer.positionStream.listen((position) {
      _broadcastState(_audioPlayer.playbackEvent);
    });

    // Handle audio completion
    _audioPlayer.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        _onAudioComplete();
      }
    });
  }

  /// Play a voice note with background support
  Future<void> playVoiceNote({
    required String voiceId,
    required VPlatformFile source,
    required Duration maxDuration,
    String? senderName,
    Function(String voiceId)? onComplete,
    Function(String voiceId)? onPlaying,
  }) async {
    try {
      _currentVoiceId = voiceId;
      _currentSource = source;
      _maxDuration = maxDuration;
      _onComplete = onComplete;
      _onPlaying = onPlaying;

      // Stop any currently playing audio
      await _audioPlayer.stop();

      // Set up media item for notification
      final mediaItem = MediaItem(
        id: voiceId,
        title: senderName ?? 'Voice Message',
        artist: 'Voice Note',
        duration: maxDuration,
        artUri: Uri.parse(
            'android.resource://com.super_up.app/drawable/ic_voice_note'),
      );

      this.mediaItem.add(mediaItem);

      // Load and play audio
      await _loadAndPlayAudio(source);

      // Notify that playback started
      _onPlaying?.call(voiceId);
    } catch (e) {
      if (kDebugMode) {
        print('Error playing voice note: $e');
      }
    }
  }

  Future<void> _loadAndPlayAudio(VPlatformFile source) async {
    AudioSource audioSource;

    if (source.isFromPath) {
      audioSource = AudioSource.file(source.fileLocalPath!);
    } else if (source.isFromBytes) {
      audioSource = AudioSource.uri(
        Uri.dataFromBytes(source.bytes!, mimeType: 'audio/mpeg'),
      );
    } else if (source.isFromUrl) {
      if (VPlatforms.isMobile) {
        // Cache the file for better performance
        final file = await DefaultCacheManager().getSingleFile(
          source.fullNetworkUrl!,
          key: source.getCachedUrlKey,
        );
        audioSource = AudioSource.file(file.path);
      } else {
        audioSource = AudioSource.uri(Uri.parse(source.fullNetworkUrl!));
      }
    } else {
      throw Exception('Unsupported audio source type');
    }

    await _audioPlayer.setAudioSource(audioSource);
    await _audioPlayer.play();
  }

  void _onAudioComplete() {
    if (_currentVoiceId != null) {
      _onComplete?.call(_currentVoiceId!);
    }
    _resetState();
  }

  void _resetState() {
    _currentVoiceId = null;
    _currentSource = null;
    _maxDuration = null;
    _onComplete = null;
    _onPlaying = null;
  }

  void _broadcastState(PlaybackEvent event) {
    final playing = _audioPlayer.playing;
    final processingState = _audioPlayer.processingState;

    PlaybackState state = PlaybackState(
      controls: [MediaControl.play],
      systemActions: const {MediaAction.seek},
      androidCompactActionIndices: const [0],
      processingState: AudioProcessingState.idle,
      playing: false,
      updatePosition: _audioPlayer.position,
      bufferedPosition: _audioPlayer.bufferedPosition,
      speed: _audioPlayer.speed,
    );

    switch (processingState) {
      case ProcessingState.idle:
        state = PlaybackState(
          controls: [MediaControl.play],
          systemActions: const {MediaAction.seek},
          androidCompactActionIndices: const [0],
          processingState: AudioProcessingState.idle,
          playing: false,
          updatePosition: _audioPlayer.position,
          bufferedPosition: _audioPlayer.bufferedPosition,
          speed: _audioPlayer.speed,
        );
        break;
      case ProcessingState.loading:
      case ProcessingState.buffering:
        state = PlaybackState(
          controls: [MediaControl.pause],
          systemActions: const {MediaAction.seek},
          androidCompactActionIndices: const [0],
          processingState: AudioProcessingState.loading,
          playing: playing,
          updatePosition: _audioPlayer.position,
          bufferedPosition: _audioPlayer.bufferedPosition,
          speed: _audioPlayer.speed,
        );
        break;
      case ProcessingState.ready:
        state = PlaybackState(
          controls: playing ? [MediaControl.pause] : [MediaControl.play],
          systemActions: const {MediaAction.seek},
          androidCompactActionIndices: const [0],
          processingState: AudioProcessingState.ready,
          playing: playing,
          updatePosition: _audioPlayer.position,
          bufferedPosition: _audioPlayer.bufferedPosition,
          speed: _audioPlayer.speed,
        );
        break;
      case ProcessingState.completed:
        state = PlaybackState(
          controls: [MediaControl.play],
          systemActions: const {MediaAction.seek},
          androidCompactActionIndices: const [0],
          processingState: AudioProcessingState.completed,
          playing: false,
          updatePosition: _audioPlayer.position,
          bufferedPosition: _audioPlayer.bufferedPosition,
          speed: _audioPlayer.speed,
        );
        break;
    }

    playbackState.add(state);
  }

  @override
  Future<void> play() async {
    await _audioPlayer.play();
  }

  @override
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  @override
  Future<void> stop() async {
    await _audioPlayer.stop();
    _resetState();
    await super.stop();
  }

  @override
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// Get current playback position
  Duration get position => _audioPlayer.position;

  /// Get current playback duration
  Duration? get duration => _audioPlayer.duration;

  /// Check if currently playing
  bool get isPlaying => _audioPlayer.playing;

  /// Check if a specific voice note is currently playing
  bool isVoiceNotePlaying(String voiceId) {
    return _currentVoiceId == voiceId && isPlaying;
  }

  /// Pause current voice note
  Future<void> pauseCurrentVoiceNote() async {
    if (_currentVoiceId != null) {
      await pause();
    }
  }

  /// Resume current voice note
  Future<void> resumeCurrentVoiceNote() async {
    if (_currentVoiceId != null) {
      await play();
    }
  }

  /// Stop current voice note
  Future<void> stopCurrentVoiceNote() async {
    if (_currentVoiceId != null) {
      await stop();
    }
  }

  /// Get position stream for UI updates
  Stream<Duration> get positionStream => _audioPlayer.positionStream;

  /// Get player state stream for UI updates
  Stream<PlayerState> get playerStateStream => _audioPlayer.playerStateStream;

  @override
  Future<void> onTaskRemoved() async {
    // Keep playing when app is removed from recent apps
    // Don't call super.onTaskRemoved() to prevent stopping
  }

  /// Dispose the service
  Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _playbackStateController.close();
    await _mediaItemController.close();
  }
}
