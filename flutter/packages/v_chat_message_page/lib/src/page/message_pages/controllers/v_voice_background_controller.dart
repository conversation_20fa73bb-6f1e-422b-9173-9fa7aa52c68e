// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_chat_voice_player/v_chat_voice_player.dart';

/// Enhanced voice controller that supports background playback
class VVoiceBackgroundPlayerController {
  final _voiceControllers = <VVoiceMessageController>[];
  final String? Function(String localId) onVoiceNeedToPlayNext;
  final VVoiceBackgroundService _backgroundService =
      VVoiceBackgroundService.instance;

  String? _currentPlayingId;
  StreamSubscription? _positionSubscription;
  StreamSubscription? _playerStateSubscription;

  VVoiceBackgroundPlayerController(this.onVoiceNeedToPlayNext);

  VVoiceMessageController? getById(String id) =>
      _voiceControllers.firstWhereOrNull((e) => e.id == id);

  VVoiceMessageController getVoiceController(VVoiceMessage voiceMessage) {
    final oldController = getById(voiceMessage.localId);

    if (oldController != null) return oldController;

    // Create a regular voice controller but intercept its play method
    final controller = VVoiceMessageController(
      id: voiceMessage.localId,
      audioSrc: voiceMessage.data.fileSource,
      onComplete: (String localId) {
        _onVoiceComplete(localId);
      },
      maxDuration: voiceMessage.data.durationObj,
      onPlaying: (String localId) {
        _onPlaying(localId);
        // Start background service when playing
        _startBackgroundPlayback(voiceMessage, localId);
      },
    );
    _voiceControllers.add(controller);
    return controller;
  }

  Future<void> _startBackgroundPlayback(
      VVoiceMessage voiceMessage, String localId) async {
    try {
      await _backgroundService.playVoiceNote(
        voiceId: localId,
        source: voiceMessage.data.fileSource,
        maxDuration: voiceMessage.data.durationObj,
        senderName: _getSenderName(voiceMessage),
        onComplete: (voiceId) {
          _onVoiceComplete(voiceId);
        },
        onPlaying: (voiceId) {
          // Already handled above
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error starting background playback: $e');
      }
    }
  }

  String _getSenderName(VVoiceMessage voiceMessage) {
    // Try to get sender name from message
    // This might need to be adjusted based on your message structure
    return 'Voice Message';
  }

  void _onPlaying(String id) {
    _currentPlayingId = id;

    // Pause all other controllers
    for (final controller in _voiceControllers) {
      if (controller.id != id) {
        controller.pausePlaying();
      }
    }
  }

  void _onVoiceComplete(String localId) {
    _currentPlayingId = null;

    // Try to play next voice note
    final nextId = onVoiceNeedToPlayNext(localId);
    if (nextId != null) {
      getById(nextId)?.initAndPlay();
    }
  }

  void pauseAll() {
    for (final c in _voiceControllers) {
      c.pausePlaying();
    }
    // Don't pause background service - let it continue playing
    // _backgroundService.pauseCurrentVoiceNote();
    _currentPlayingId = null;
  }

  /// Pause all voice notes including background service
  void pauseAllIncludingBackground() {
    for (final c in _voiceControllers) {
      c.pausePlaying();
    }
    _backgroundService.pauseCurrentVoiceNote();
    _currentPlayingId = null;
  }

  void close() {
    _positionSubscription?.cancel();
    _playerStateSubscription?.cancel();

    for (final c in _voiceControllers) {
      c.dispose();
    }

    // Don't stop the background service when closing the controller
    // This allows voice notes to continue playing when navigating away
    _currentPlayingId = null;
  }

  /// Check if any voice note is currently playing
  bool get isAnyPlaying => _currentPlayingId != null;

  /// Get currently playing voice note ID
  String? get currentPlayingId => _currentPlayingId;
}
